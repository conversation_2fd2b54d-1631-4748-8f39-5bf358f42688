/**
 * Calculate all currency prices based on a base price and currency
 * 
 * @param basePrice - The price in the base currency
 * @param baseCurrency - The base currency code (USD, JPY, KRW)
 * @returns Object containing prices in all supported currencies
 */
export const  calculateAllPrices = async (basePrice: number, baseCurrency: string) => {
  // Fetch current exchange rates from website_settings
  const settings = await elevatedFetch<BackendResponse<WebsiteSettings>>('/items/website_settings');
  const rates = settings.data;

  if (!rates.jpy_exchange_rate || !rates.krw_exchange_rate) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Exchange rates not available in website settings',
    });
  }

  const prices = {
    price: 0,
    price_jpy: 0,
    price_krw: 0,
  };

  // Set the base currency price and calculate others
  switch (baseCurrency.toUpperCase()) {
    case 'USD':
      prices.price = basePrice;
      prices.price_jpy = Math.ceil(basePrice * rates.jpy_exchange_rate);
      prices.price_krw = Math.ceil(basePrice * rates.krw_exchange_rate);
      break;
    case 'JPY':
      prices.price_jpy = basePrice;
      prices.price = Math.ceil((basePrice / rates.jpy_exchange_rate) * 100) / 100;
      prices.price_krw = Math.ceil((basePrice / rates.jpy_exchange_rate) * rates.krw_exchange_rate);
      break;
    case 'KRW':
      prices.price_krw = basePrice;
      prices.price = Math.ceil((basePrice / rates.krw_exchange_rate) * 100) / 100;
      prices.price_jpy = Math.ceil((basePrice / rates.krw_exchange_rate) * rates.jpy_exchange_rate);
      break;
    default:
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid currency. Supported currencies: USD, JPY, KRW',
      });
  }

  return prices;
}
