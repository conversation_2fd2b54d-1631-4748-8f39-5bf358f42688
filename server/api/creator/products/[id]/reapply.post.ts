import { z } from 'zod/v4';

// Product reapply schema - matches the form validation
const productReapplySchema = z.object({
  name: z.string().min(3),
  description: z.string().min(10),
  category: z.number().min(1),
  applicationType: z.number().min(1),
  images: z.array(z.string()).min(1).optional(),
  price: z.number().min(1),
  currency: z.string().min(1),
  totalStock: z.number().min(1),
  locale: z.enum(['en', 'jp', 'kr']).default('en'),
});

export default defineEventHandler(async (event) => {
  // Check authentication
  const accessToken = getCookie(event, 'access_token');

  if (!accessToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
    });
  }

  // Get product ID from route params
  const productId = getRouterParam(event, 'id');
  
  if (!productId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Product ID is required',
    });
  }

  // Validate request body
  const body = await readValidatedBody(event, productReapplySchema.parse);

  try {
    // Get current user for the new product creation
    const currentUser = await dFetch<BackendResponse<DirectusUsers>>('/users/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id', 'creator_profile.*'],
      },
    });

    
    const creatorProfile = currentUser.data.creator_profile?.[0];
    
    if (!creatorProfile) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have a creator profile',
      });
    }

    // Get the existing product data for IP relationship (RBAC will handle authorization)
    const existingProduct = await dFetch<BackendResponse<Products>>(`/items/products/${productId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        fields: ['id', 'ip'],
      },
    });

    // Calculate all currency prices based on the selected base currency
    const calculatedPrices = await calculateAllPrices(body.price, body.currency);

    // Step 1: Create a new product with the updated data
    const newProductData = {
      category: body.category,
      product_application_category: body.applicationType,
      ...calculatedPrices, // Include all calculated prices (price, price_jpy, price_krw)
      base_currency: body.currency,
      stock_total: body.totalStock,
      stock_remaining: body.totalStock,
      main_image: body.images?.[0], // Set first image as main image
      status: 'pending', // New product starts as pending
      creator: creatorProfile.id,
      ip: typeof existingProduct.data.ip === 'number' ? existingProduct.data.ip : existingProduct.data.ip?.id,
      user_created: currentUser.data.id,
      keywords: body.name + ' ' + body.description,
    };

    const newProduct = await elevatedFetch<BackendResponse<Products>>('/items/products', {
      method: 'POST',
      body: newProductData,
    });

    // Step 2: Create translation for the new product
    const currentLanguageId = LANGUAGE_LOCAL_ID[body.locale as keyof typeof LANGUAGE_LOCAL_ID];
    await elevatedFetch('/items/products_translations', {
      method: 'POST',
      body: {
        name: body.name,
        description: body.description,
        languages_id: currentLanguageId,
        products_id: newProduct.data.id,
      },
    });

    // Step 3: Create product_files entries for the new product
    if (body.images && body.images.length > 0) {
      const imagePromises = body.images.map((imageId: string) =>
        elevatedFetch('/items/products_files', {
          method: 'POST',
          body: {
            directus_files_id: imageId,
            products_id: newProduct.data.id,
          },
        })
      );
      await Promise.all(imagePromises);
    }

    // Step 4: Archive the existing product after successful creation (RBAC will handle authorization)
    await dFetch(`/items/products/${productId}`, {
      method: 'PATCH',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      body: {
        status: 'archived',
      },
    });

    return newProduct.data

  } catch (error) {
    console.error('Failed to reapply product:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to reapply product',
    });
  }
});
