{% assign title = "New Product Application" %}
{% assign header_title = "New Product Application Submitted" %}
{% assign footer_message = "This is an automated notification for a new product application submission." %}

{% layout "base" %}
{% block content %}
<div class="content">
  <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 24px; border-left: 4px solid #28a745;">
    <p style="margin: 0 0 12px 0; font-size: 16px; color: #333;">
      <strong>Thank you for submitting your product application!</strong>
    </p>
    <p style="margin: 0; color: #666; line-height: 1.5;">
      Your application has been successfully received and will be reviewed by the IP owner shortly.
      You'll receive a notification once the review is complete. We appreciate your interest in creating
      products for this IP and look forward to potentially working with you.
    </p>
  </div>

  <h2>Application Details</h2>

  <div class="info-row">
    <span class="info-label">IP:</span>
    <span class="info-value">{{ ip_name | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Creator's Username:</span>
    <span class="info-value">{{ creator_username | default: "N/A" }}</span>
  </div>

  {% comment %} <div class="info-row">
    <span class="info-label">Creator's Email:</span>
    <span class="info-value">{{ creator_email | default: "N/A" }}</span>
  </div> {% endcomment %}

  <div class="info-row">
    <span class="info-label">Product Name:</span>
    <span class="info-value">{{ product_name | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Product Description:</span>
    <span class="info-value">{{ product_description | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Product Category:</span>
    <span class="info-value">{{ product_category | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Application Type:</span>
    <span class="info-value">{{ application_type | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Price:</span>
    <span class="info-value">{{ currency }}{{ price | default: "N/A" }}</span>
  </div>

  <div class="info-row">
    <span class="info-label">Total Stock:</span>
    <span class="info-value">{{ total_stock | default: 0 }}</span>
  </div>

  {% if images and images.size > 0 %}
  <div class="images-section">
    <div class="info-label">Design / Images:</div>
    <div style="margin-top: 10px;">
      {% for image in images %}
        <img src="{{ base_url | default: 'https://stg-admin.ipgo.space' }}/assets/{{ image.directus_files_id }}?width=400" alt="Product Image" style="max-width: 200px; margin: 5px; border-radius: 4px;">
        {% unless forloop.last %}<br>{% endunless %}
      {% endfor %}
    </div>
  </div>
  {% else %}
  <div class="images-section">
    <div class="info-label">Design / Images:</div>
    <div style="margin-top: 10px;">
      <p>No images provided</p>
    </div>
  </div>
  {% endif %}
</div>
{% endblock %}

{% comment %}
Required payload data example:
{
  "base_url": "https://admin.ipgo.space",
  "ip_name": "My Awesome IP",
  "creator_username": "creator123",
  "creator_email": "<EMAIL>",
  "product_name": "Limited Edition Figure",
  "product_description": "High-quality collectible figure with detailed craftsmanship",
  "product_category": "Figures & Collectibles",
  "application_type": "New Product",
  "currency": "$",
  "price": "49.99",
  "total_stock": 100,
  "images": [
    {
      "directus_files_id": "abc123-def456-ghi789"
    },
    {
      "directus_files_id": "xyz789-uvw456-rst123"
    }
  ]
}

Note: base_url parameter is optional and defaults to "https://stg-admin.ipgo.space" if not provided.
{% endcomment %}
