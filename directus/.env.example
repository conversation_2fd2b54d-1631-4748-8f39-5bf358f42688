# Directus Configuration
# Copy this file to .env and fill in your actual values

# Core Directus Settings
DIRECTUS_KEY=h1YkiihoP6YgLIvvfrTLt8QRuC5gkfcfrnkGFDrH36kx5ZA1GbSW5j1PIGrGjGHa
DIRECTUS_SECRET=WdJ9o3gU1BsZVGG6USWiMCcl38sytBcCssbYX4sI5xm9BYTpBa3qV0Jd9NF17lX5

# Optional Settings (with defaults)
EXTENSIONS_AUTO_RELOAD=true
ACCESS_TOKEN_TTL=7d

# Content Security Policy
CSP_FRAME_SRC='http://localhost:8055 https://rafazafar-ipgo-nuxt.zafar.workers.dev https://localhost:3000 https://stg.ipgo.space'
CSP_CHILD_SRC='https://rafazafar-ipgo-nuxt.zafar.workers.dev https://docs.google.com https://stg-admin.ipgo.space http://localhost:3000 https://stg.ipgo.space'

# Database Configuration
DB_HOST=aws-0-ap-northeast-1.pooler.supabase.com
DB_PORT=5432
DB_DATABASE=smg_ipgo_directus
DB_USER=postgres.xibhkrtmzhazxwwnwsoy
DB_PASSWORD=?Jt4oYPytsbH

# File Upload Settings
FILES_MAX_UPLOAD_SIZE=100mb

# Admin User
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=directus

# CORS Settings
CORS_ENABLED=true
CORS_ORIGIN=https://ipgo.space,http://localhost:8055,http://localhost:3000,https://rafazafar-ipgo-nuxt.zafar.workers.dev,https://stg.ipgo.space
CORS_METHODS=GET,POST,PATCH,DELETE
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_EXPOSED_HEADERS=Content-Range,X-Content-Range

# Cache Settings
CACHE_ENABLED=false
CACHE_TTL=1m
CACHE_AUTO_PURGE=true

# Email Configuration
EMAIL_VERIFY_SETUP=true
EMAIL_FROM=<EMAIL>
EMAIL_TRANSPORT=smtp

# SMTP Settings
EMAIL_SMTP_SECURE=false
EMAIL_SMTP_HOST=email-smtp.ap-northeast-1.amazonaws.com
EMAIL_SMTP_PASSWORD=BKO39T33YnRAjUntg6jTyFSAoPdEuePMyb1VcbapvXCs
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=********************

# SES Settings (Alternative to SMTP - currently commented out)
# EMAIL_SES_ACCESS_KEY_ID=your_ses_access_key
# EMAIL_SES_SECRET_ACCESS_KEY=your_ses_secret_key
# EMAIL_SES_REGION=ap-northeast-1

# Public URL
PUBLIC_URL=https://stg-admin.ipgo.space

# AWS S3 Storage Configuration
STORAGE_LOCATIONS=AWS
STORAGE_AWS_DRIVER=s3
STORAGE_AWS_KEY=********************
STORAGE_AWS_SECRET=xhxyX61t3NGoD64F4wjG06VyCQfgFUePNUuLP3RC
STORAGE_AWS_BUCKET=ipgo-be-api-staging-bucket
STORAGE_AWS_REGION=ap-northeast-1
STORAGE_AWS_SERVER_SIDE_ENCRYPTION=false

# OAuth Settings (currently commented out in docker-compose.yml)
# Uncomment and configure these if you want to enable OAuth providers

# Google OAuth
# AUTH_PROVIDERS=google,linkedin,github
# AUTH_GOOGLE_MODE=json
# AUTH_GOOGLE_DRIVER=openid
# AUTH_GOOGLE_CLIENT_ID=***********-no1rvo0aqkals7glqf8th732ln698d3s.apps.googleusercontent.com
# AUTH_GOOGLE_CLIENT_SECRET=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
# AUTH_GOOGLE_ISSUER_URL=https://accounts.google.com/.well-known/openid-configuration
# AUTH_GOOGLE_IDENTIFIER_KEY=email
# AUTH_GOOGLE_ALLOW_PUBLIC_REGISTRATION=true
# AUTH_GOOGLE_DEFAULT_ROLE_ID=38d4fed7-ba46-44d3-a69c-617e121823cd
# AUTH_GOOGLE_REDIRECT_ALLOW_LIST=http://localhost:3001/webhooks/auth/google

# LinkedIn OAuth
# AUTH_LINKEDIN_DRIVER=oauth2
# AUTH_LINKEDIN_CLIENT_ID=77exp7mnr97w2c
# AUTH_LINKEDIN_CLIENT_SECRET=qMkAiC7LBKxYxmH0
# AUTH_LINKEDIN_IDENTIFIER_KEY=r_emailaddress
# AUTH_LINKEDIN_AUTHORIZE_URL=https://www.linkedin.com/oauth/v2/authorization
# AUTH_LINKEDIN_ACCESS_URL=https://www.linkedin.com/oauth/v2/accessToken
# AUTH_LINKEDIN_PROFILE_URL=https://api.linkedin.com/v2/me
# AUTH_LINKEDIN_SCOPE=r_emailaddress,r_liteprofile
# AUTH_LINKEDIN_ALLOW_PUBLIC_REGISTRATION=true
# AUTH_LINKEDIN_DEFAULT_ROLE_ID=38d4fed7-ba46-44d3-a69c-617e121823cd
# AUTH_LINKEDIN_REDIRECT_ALLOW_LIST=http://localhost:3001/webhooks/auth/linkedin

# GitHub OAuth
# AUTH_GITHUB_DRIVER=oauth2
# AUTH_GITHUB_CLIENT_ID=********************
# AUTH_GITHUB_CLIENT_SECRET=4c37b004346849fe68f35b9c280a53df15cb017b
# AUTH_GITHUB_AUTHORIZE_URL=https://github.com/login/oauth/authorize
# AUTH_GITHUB_ACCESS_URL=https://github.com/login/oauth/access_token
# AUTH_GITHUB_PROFILE_URL=https://api.github.com/user
# AUTH_GITHUB_ALLOW_PUBLIC_REGISTRATION=true
# AUTH_GITHUB_DEFAULT_ROLE_ID=38d4fed7-ba46-44d3-a69c-617e121823cd
# AUTH_GITHUB_REDIRECT_ALLOW_LIST=http://localhost:3001/webhooks/auth/github

# Cookie Settings (for production)
# REFRESH_TOKEN_COOKIE_DOMAIN=directus.seekers.my
# REFRESH_TOKEN_COOKIE_SECURE=true
# REFRESH_TOKEN_COOKIE_SAME_SITE=None
