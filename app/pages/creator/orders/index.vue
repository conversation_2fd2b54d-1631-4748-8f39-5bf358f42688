<template>
  <UPage>
    <UPageBody v-auto-animate>
        <div class="flex flex-row gap-4 text-xl font-bold">
          <!-- Should be red if active -->
          <button
            @click="mode = 'ongoing'"
            :active="mode === 'ongoing'"
            class="cursor-pointer"
            :class="mode === 'ongoing' ? 'text-primary' : ''"
          >
            {{ $t('global.ongoing') }}
          </button>
          <button
            @click="mode = 'completed'"
            :active="mode === 'completed'"
            class="cursor-pointer"
            :class="mode === 'completed' ? 'text-primary' : ''"
          >
            {{ $t('global.completed') }}
          </button>
        </div>
        <div id="orders-headers" class="grid grid-cols-6 gap-4 p-4 text-sm text-neutral-500 -mt-8">
          <p>{{ $t('global.preview_image') }}</p>
          <p>{{ $t('global.product_name') }}</p>
          <p>{{ $t('global.product_category') }}</p>
          <p>{{ $t('global.date_ordered') }}</p>
          <p>{{ $t('global.status') }}</p>
          <p>{{ $t('global.operation') }}</p>
        </div>

        <!-- Empty state -->
        <div v-if="!orders || orders.length === 0" class="flex flex-col items-center justify-center py-16 text-center">
          <UIcon name="heroicons:shopping-bag" class="mb-4 size-16 text-neutral-400" />
          <h3 class="mb-2 text-lg font-semibold text-neutral-300">
            {{ mode === 'ongoing' ? $t('global.no_ongoing_orders') : $t('global.no_completed_orders') }}
          </h3>
          <p class="mb-6 text-neutral-500">
            {{
              mode === 'ongoing' ? $t('global.no_orders_found') : $t('global.no_completed_orders_description')
            }}
          </p>
        </div>

        <!-- Orders list -->
        <div v-else class="-mt-12">
          <div v-for="order in orders" :key="order.id">
            <div id="order-card" class="mb-6 rounded-lg border border-neutral-700 p-4">
              <!-- Order items grid -->
              <div class="grid gap-4">
                <div
                  :id="`item-${item.id}`"
                  v-for="(item, index) in order.order_items"
                  :key="item.id"
                  class="grid grid-cols-6 items-center gap-4"
                >
                  <div class="col-span-3 md:col-span-1">
                    <NuxtImg
                      :src="directusAssetsUrl(item.product?.main_image as string, 200, 200) ?? '/images/missing-product.png'"
                      class="h-32 w-full rounded-lg object-cover"
                      placeholder
                      placeholder-class="blur-xs"
                    />
                  </div>
                  <div class="col-span-3 md:col-span-1">
                    <p>{{ item.product_name_at_order }}</p>
                    <!-- IP Name -->
                    <p class="text-xs text-neutral-400">{{ useTranslatedName(item.product?.ip?.translations) }}</p>
                    <p class="text-xs text-neutral-400">x{{ item.quantity }}</p>
                  </div>
                  <div class="col-span-2 md:col-span-1 text-sm md:text-md">
                    <p>{{ useTranslatedName((item.product?.category as IpCategories).translations) }}</p>
                  </div>
                  <div class="col-span-2 md:col-span-1 text-sm md:text-md">
                    <p>{{ format(new Date(order.date_created!), 'yyyy-MM-dd') }}</p>
                  </div>
                  <!-- Use shared status color utility -->
                  <div class="col-span-2 md:col-span-1 text-sm md:text-md">
                    <UBadge
                      class="justify-center px-4 capitalize"
                      variant="subtle"
                      :color="getStatusColor(order.status)"
                      >{{ $t(`global.${getOrderStatusKey(order.status)}`) }}</UBadge
                    >
                  </div>
                  <!-- Operations column - show actions only for first item on desktop, hidden on mobile -->
                  <div class="hidden md:block md:col-span-1">
                    <div v-if="index === 0" class="flex flex-col gap-1">
                      <UButton
                        :to="$localePath({ name: 'creator-orders-id', params: { id: order.id } })"
                        variant="ghost"
                        class="w-full text-left text-xs justify-start"
                        leading-icon="heroicons:chat-bubble-oval-left"
                      >
                        {{ $t('global.chat') }}
                      </UButton>
                      <CreatorShippedOrderModal
                        :orderId="order.id"
                        @confirm="refresh"
                        v-if="order.status !== 'shipped' && order.status !== 'completed'"
                      >
                        <UButton
                          variant="ghost"
                          class="w-full text-left text-xs justify-start"
                          leading-icon="heroicons:check-circle"
                        >
                          {{ $t('global.complete_order') }}
                        </UButton>
                      </CreatorShippedOrderModal>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Mobile-only order actions - shown below all items -->
              <div class="md:hidden flex flex-row gap-2 pt-4 border-t border-neutral-700 mt-4">
                <UButton
                  :to="$localePath({ name: 'creator-orders-id', params: { id: order.id } })"
                  variant="ghost"
                  class="flex-1 text-xs justify-center"
                  leading-icon="heroicons:chat-bubble-oval-left"
                >
                  {{ $t('global.chat') }}
                </UButton>
                <CreatorShippedOrderModal
                  :orderId="order.id"
                  @confirm="refresh"
                  v-if="order.status !== 'shipped' && order.status !== 'completed'"
                >
                  <UButton
                    variant="ghost"
                    class="flex-1 text-xs justify-center"
                    leading-icon="heroicons:check-circle"
                  >
                    {{ $t('global.complete_order') }}
                  </UButton>
                </CreatorShippedOrderModal>
              </div>
            </div>
          </div>
        </div>
    </UPageBody>
  </UPage>
</template>

<script lang="ts" setup>
import { format } from 'date-fns';
import { getStatusColor } from '~~/shared/utils/ui';
definePageMeta({
  layout: 'creator',
  middleware: 'creator-auth',
  title: 'pages.creator.orders.title',
});
const { directusAssetsUrl } = useDirectus();
const mode = ref('ongoing');

// Helper function to get correct translation key for order status
const getOrderStatusKey = (status: string) => {
  const statusKeyMap: Record<string, string> = {
    pending: 'pending',
    processing: 'not_yet_shipped',
    shipped: 'shipped',
    completed: 'completed',
    cancelled: 'cancelled'
  };
  return statusKeyMap[status] || status;
};

// Fetch orders with reactive status filtering
const { data: orders, refresh } = await useFetch<Orders[]>('/api/creator/orders', {
  query: {
    status: mode,
  },
  key: `my-orders-${mode.value}`,
  watch: [mode],
});
</script>
